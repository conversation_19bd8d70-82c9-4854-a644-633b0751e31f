[project]
name = "openpi-client"
version = "0.1.0"
requires-python = ">=3.7"
dependencies = [
    "dm-tree>=0.1.8",
    "msgpack>=1.0.5",
    "numpy>=1.21.6",
    "pillow>=9.0.0",
    "tree>=0.2.4",
    "websockets>=11.0",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.uv]
dev-dependencies = [
    "pytest>=8.3.4",
]

[tool.ruff]
line-length = 120
target-version = "py37"