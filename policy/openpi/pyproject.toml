[project]
name = "openpi"
version = "0.1.0"
description = "Physical Intelligence open source repo"
readme = "README.md"
requires-python = ">=3.11"
license = { file = "LICENSE" }
dependencies = [
    "pip==24.2",
    "augmax>=0.3.4",
    "dm-tree>=0.1.8",
    "einops>=0.8.0",
    "equinox>=0.11.8",
    "flatbuffers>=24.3.25",
    "flax==0.10.2",
    "fsspec[gcs]>=2024.6.0",
    "gym-aloha>=0.1.1",
    "imageio>=2.36.1",
    "jax[cuda12]==0.5.0",
    "jaxtyping==0.2.36",
    "lerobot",
    "ml_collections==1.0.0",
    "numpy==1.26.4",
    "numpydantic>=1.6.6",
    "opencv-python>=*********",
    "openpi-client",
    "orbax-checkpoint==0.11.1",
    "pillow>=11.0.0",
    "s3fs>=2024.9.0",
    "sentencepiece>=0.2.0",
    "torch>=2.5.1",
    "tqdm-loggable>=0.2",
    "typing-extensions>=4.12.2",
    "tyro>=0.9.5",
    "wandb>=0.19.1",
    "boto3>=1.35.7",
    "types-boto3[boto3,s3]>=1.35.7",
    "filelock>=3.16.1",
    "beartype>=0.19.0",
    "treescope>=0.1.7",
    "transformers==4.48.1",
    "sapien==3.0.0b1",
    "scipy",
    "mplib==0.1.1",
    "gymnasium==0.29.1",
    "trimesh==4.4.3",
    "open3d==0.19.0",
    "imageio",
    "pydantic",
    "zarr",
    "openai",
    "huggingface_hub",
    "ffmpeg",
]


[project.urls]
Repository = "https://github.com/Physical-Intelligence/openpi"

[dependency-groups]
dev = [
    "pytest>=8.3.4",
    "ruff>=0.8.6",
    "pre-commit>=4.0.1",
    "ipykernel>=6.29.5",
    "ipywidgets>=8.1.5",
    "matplotlib>=3.10.0",
    "pynvml>=12.0.0",
]


[tool.uv.sources]
openpi-client = { workspace = true }
lerobot = { git = "https://github.com/huggingface/lerobot", rev = "6674e368249472c91382eb54bb8501c94c7f0c56" }

[tool.uv.workspace]
members = ["packages/*"]

[tool.ruff]
line-length = 120
target-version = "py311"
extend-exclude = ["docker", "third_party"]

[tool.ruff.lint]
# https://docs.astral.sh/ruff/rules/
select = [
    "B",
    "C4",
    "DTZ",
    "E4",
    "E7",
    "E9",
    "F",
    "FBT",
    "FURB",
    "I",
    "ICN",
    "ISC",
    "LOG",
    "N",
    "PD",
    "PERF",
    "PIE",
    "PLC",
    "PLE",
    "PLR1",
    "PLR5",
    "PLW",
    "PT",
    "PTH",
    "Q",
    "RET",
    "RUF",
    "SIM",
    "SLF",
    "T10",
    "T20",
    "UP",
    "W",
]
ignore = [
    "F722",   # Conflicts with array typing.
    "T201",   # We use print statements.
    "PD008",  # Lots of false positives.
    "ISC001", # Disabling to support ruff format.
]
unfixable = [
    "B905", # Fix defaults to strict=False, which is not what we want.
]

[tool.ruff.lint.isort]
force-single-line = true
force-sort-within-sections = true
single-line-exclusions = ["collections.abc", "typing", "typing_extensions"]
known-third-party = ["wandb"]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.pytest.ini_options]
markers = ["manual: should be run manually."]
testpaths = ["src", "scripts", "packages"]
