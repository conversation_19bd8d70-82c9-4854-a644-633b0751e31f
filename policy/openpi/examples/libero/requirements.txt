# This file was autogenerated by uv via the following command:
#    uv pip compile examples/libero/requirements.in -o examples/libero/requirements.txt --python-version 3.8 --index-strategy=unsafe-best-match
absl-py==2.1.0
    # via mujoco
certifi==2024.12.14
    # via requests
charset-normalizer==3.4.0
    # via requests
cycler==0.12.1
    # via matplotlib
docstring-parser==0.16
    # via tyro
etils==1.3.0
    # via mujoco
eval-type-backport==0.2.0
    # via tyro
evdev==1.7.1
    # via pynput
fonttools==4.55.3
    # via matplotlib
glfw==1.12.0
    # via mujoco
idna==3.10
    # via requests
imageio==2.35.1
    # via -r examples/libero/requirements.in
imageio-ffmpeg==0.5.1
    # via imageio
importlib-metadata==8.5.0
    # via typeguard
importlib-resources==6.4.5
    # via etils
kiwisolver==1.4.7
    # via matplotlib
llvmlite==0.36.0
    # via numba
markdown-it-py==3.0.0
    # via rich
matplotlib==3.5.3
    # via -r examples/libero/requirements.in
mdurl==0.1.2
    # via markdown-it-py
mujoco==3.2.3
    # via robosuite
numba==0.53.1
    # via robosuite
numpy==1.22.4
    # via
    #   -r examples/libero/requirements.in
    #   imageio
    #   matplotlib
    #   mujoco
    #   numba
    #   opencv-python
    #   robosuite
    #   scipy
    #   torchvision
opencv-python==********
    # via
    #   -r examples/libero/requirements.in
    #   robosuite
packaging==24.2
    # via matplotlib
pillow==10.4.0
    # via
    #   imageio
    #   matplotlib
    #   robosuite
    #   torchvision
psutil==6.1.0
    # via imageio
pygments==2.18.0
    # via rich
pynput==1.7.7
    # via robosuite
pyopengl==3.1.7
    # via mujoco
pyparsing==3.1.4
    # via matplotlib
python-dateutil==2.9.0.post0
    # via matplotlib
python-xlib==0.33
    # via pynput
pyyaml==6.0.2
    # via -r examples/libero/requirements.in
requests==2.32.3
    # via torchvision
rich==13.9.4
    # via tyro
robosuite==1.4.1
    # via -r examples/libero/requirements.in
scipy==1.10.1
    # via robosuite
setuptools==75.3.0
    # via
    #   imageio-ffmpeg
    #   numba
shtab==1.7.1
    # via tyro
six==1.17.0
    # via
    #   pynput
    #   python-dateutil
    #   python-xlib
termcolor==2.4.0
    # via robosuite
torch==1.11.0+cu113
    # via
    #   -r examples/libero/requirements.in
    #   torchaudio
    #   torchvision
torchaudio==0.11.0+cu113
    # via -r examples/libero/requirements.in
torchvision==0.12.0+cu113
    # via -r examples/libero/requirements.in
tqdm==4.67.1
    # via -r examples/libero/requirements.in
typeguard==4.4.0
    # via tyro
typing-extensions==4.12.2
    # via
    #   etils
    #   rich
    #   torch
    #   torchvision
    #   typeguard
    #   tyro
tyro==0.9.2
    # via -r examples/libero/requirements.in
urllib3==2.2.3
    # via requests
zipp==3.20.2
    # via
    #   etils
    #   importlib-metadata
    #   importlib-resources
