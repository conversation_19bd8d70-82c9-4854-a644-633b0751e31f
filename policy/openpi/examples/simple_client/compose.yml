# Run with:
# docker compose -f examples/simple_client/compose.yml up --build
services:
  runtime:
    image: simple_client
    depends_on:
      - openpi_server
    build:
      context: ../..
      dockerfile: examples/simple_client/Dockerfile
    init: true
    tty: true
    network_mode: host
    volumes:
      - $PWD:/app
    environment:
      - SERVER_ARGS  

  openpi_server:
    image: openpi_server
    build:
      context: ../..
      dockerfile: scripts/docker/serve_policy.Dockerfile
    init: true
    tty: true
    network_mode: host
    volumes:
      - $PWD:/app
      - ${OPENPI_DATA_HOME:-~/.cache/openpi}:/openpi_assets
    environment:
      - SERVER_ARGS
      - OPENPI_DATA_HOME=/openpi_assets
      - IS_DOCKER=true

    # Comment out this block if not running on a machine with GPUs.
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
