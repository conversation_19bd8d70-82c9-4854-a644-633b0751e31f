# This file was autogenerated by uv via the following command:
#    uv pip compile examples/aloha_sim/requirements.in -o examples/aloha_sim/requirements.txt --python-version 3.10
absl-py==2.1.0
    # via
    #   dm-control
    #   dm-env
    #   labmaze
    #   mujoco
certifi==2024.8.30
    # via requests
charset-normalizer==3.4.0
    # via requests
cloudpickle==3.1.0
    # via gymnasium
contourpy==1.3.1
    # via matplotlib
cycler==0.12.1
    # via matplotlib
dm-control==1.0.14
    # via gym-aloha
dm-env==1.6
    # via dm-control
dm-tree==0.1.8
    # via
    #   dm-control
    #   dm-env
docstring-parser==0.16
    # via tyro
farama-notifications==0.0.4
    # via gymnasium
fonttools==4.55.2
    # via matplotlib
glfw==2.8.0
    # via
    #   dm-control
    #   mujoco
gym-aloha==0.1.1
    # via -r examples/aloha_sim/requirements.in
gymnasium==1.0.0
    # via gym-aloha
idna==3.10
    # via requests
imageio==2.36.1
    # via
    #   -r examples/aloha_sim/requirements.in
    #   gym-aloha
imageio-ffmpeg==0.5.1
    # via imageio
kiwisolver==1.4.7
    # via matplotlib
labmaze==1.0.6
    # via dm-control
lxml==5.3.0
    # via dm-control
markdown-it-py==3.0.0
    # via rich
matplotlib==3.9.3
    # via -r examples/aloha_sim/requirements.in
mdurl==0.1.2
    # via markdown-it-py
msgpack==1.1.0
    # via -r examples/aloha_sim/requirements.in
mujoco==2.3.7
    # via
    #   dm-control
    #   gym-aloha
numpy==1.26.4
    # via
    #   -r examples/aloha_sim/requirements.in
    #   contourpy
    #   dm-control
    #   dm-env
    #   gymnasium
    #   imageio
    #   labmaze
    #   matplotlib
    #   mujoco
    #   scipy
packaging==24.2
    # via matplotlib
pillow==11.0.0
    # via
    #   imageio
    #   matplotlib
protobuf==5.29.1
    # via dm-control
psutil==6.1.0
    # via imageio
pygments==2.18.0
    # via rich
pyopengl==3.1.7
    # via
    #   dm-control
    #   mujoco
pyparsing==3.2.0
    # via
    #   dm-control
    #   matplotlib
python-dateutil==2.9.0.post0
    # via matplotlib
requests==2.32.3
    # via dm-control
rich==13.9.4
    # via tyro
scipy==1.14.1
    # via dm-control
setuptools==75.6.0
    # via
    #   dm-control
    #   imageio-ffmpeg
    #   labmaze
shtab==1.7.1
    # via tyro
six==1.17.0
    # via python-dateutil
tqdm==4.67.1
    # via dm-control
typeguard==4.4.1
    # via tyro
typing-extensions==4.12.2
    # via
    #   -r examples/aloha_sim/requirements.in
    #   gymnasium
    #   rich
    #   typeguard
    #   tyro
tyro==0.9.2
    # via -r examples/aloha_sim/requirements.in
urllib3==2.2.3
    # via requests
websockets==14.1
    # via -r examples/aloha_sim/requirements.in
