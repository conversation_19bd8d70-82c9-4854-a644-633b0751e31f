import os, yaml

names = [
    'block_hammer_beat',
    'block_handover',
    'blocks_stack_easy',
    'blocks_stack_hard',
    'bottle_adjust',
    'container_place',
    'diverse_bottles_pick',
    'dual_bottles_pick_easy',
    'dual_bottles_pick_hard',
    'dual_shoes_place',
    'empty_cup_place',
    'mug_hanging_hard',
    'mug_hanging_easy',
    'tool_adjust',
    'pick_apple_messy',
    'put_apple_cabinet',
    'shoe_place',
]
for task_name in names:
    task_config_path = f'../task_config/{task_name}.yml'
    data = {
        'task_name': task_name,
        'render_freq': 0,
        'eval_video_log': False,
        'use_seed': False,
        'collect_data': True,
        'save_path': './data',
        'dual_arm': True,
        'st_episode': 0,
        'head_camera_type': 'D435', # L515, D435, others
        'wrist_camera_type': 'D435', # L515, D435, others
        'front_camera_type': 'D435', # L515, D435, others
        'pcd_crop': True ,
        'pcd_down_sample_num': 1024,
        'episode_num': 100,
        'save_freq': 15,
        'save_type':{
            'raw_data': False,
            'pkl': True
        },
        'data_type':{
            'rgb': True,
            'observer': False,
            'depth': True,
            'pointcloud': True,
            'endpose': True,
            'qpos': True,
            'mesh_segmentation': False,
            'actor_segmentation': False,
        }
    }
    with open(task_config_path, 'w') as f:
        yaml.dump(data,f,default_flow_style = False,sort_keys=False)