import sys
sys.path.append('./policy/RDT/')

import os
import h5py
import numpy as np
import pickle
import cv2
import argparse
import pdb
from scripts.encode_lang_batch_once import encode_lang

def images_encoding(imgs):
    encode_data = []
    padded_data = []
    max_len = 0
    for i in range(len(imgs)):
        success, encoded_image = cv2.imencode('.jpg', imgs[i])
        jpeg_data = encoded_image.tobytes()
        encode_data.append(jpeg_data)
        max_len = max(max_len, len(jpeg_data))
    # padding
    for i in range(len(imgs)):
        padded_data.append(encode_data[i].ljust(max_len, b'\0'))
    return encode_data, max_len

def data_transform(path, episode_num, save_path):
    begin = 0
    floders =  os.listdir(path)
    assert episode_num <= len(floders), "data num not enough"

    if not os.path.exists(save_path):
        os.makedirs(save_path)
    
    for i in range(episode_num): 
        subfolder_name = f"episode{i}"
        subfolder_path = os.path.join(path, subfolder_name)
        qpos = []
        actions = []
        cam_high = []
        cam_right_wrist = []
        cam_left_wrist = []

        if os.path.isdir(subfolder_path): 
            episode = []
            pkl_files = [f for f in os.listdir(subfolder_path) if f.endswith('.pkl')] 
            last_state = None
            for j in range(0, len(pkl_files)): 
                pkl_file_path = os.path.join(subfolder_path, f'{j}.pkl')
                with open(pkl_file_path, 'rb') as pkl_f:
                    data = pickle.load(pkl_f)

                state = np.array(data['joint_action'])  # joints angle      
                state[6] /= 0.045
                state[13] /= 0.045
                state = state.astype(np.float32)
                qpos.append(state)
                
                action = state
                actions.append(action)

                camera_high= data['observation']['head_camera']['rgb']
                camera_high = camera_high[:,:,::-1]
                camera_high_resized = cv2.resize(camera_high, (640,480))
                cam_high.append(camera_high_resized)
                
                camera_right_wrist = data['observation']['right_camera']['rgb']
                camera_right_wrist = camera_right_wrist[:,:,::-1]
                camera_right_wrist_resized = cv2.resize(camera_right_wrist, (640,480))
                cam_right_wrist.append(camera_right_wrist_resized)
           
                camera_left_wrist = data['observation']['left_camera']['rgb']
                camera_left_wrist = camera_left_wrist[:,:,::-1]
                camera_left_wrist_resized = cv2.resize(camera_left_wrist, (640,480))
                cam_left_wrist.append(camera_left_wrist_resized)

        hdf5path = os.path.join(save_path, f'episode_{i}.hdf5')
        with h5py.File(hdf5path, 'w') as f:
            f.create_dataset('action', data=np.array(actions))
            obs = f.create_group('observations')
            obs.create_dataset('qpos', data=np.array(qpos))
            image = obs.create_group('images')
            cam_high_enc, len_high = images_encoding(cam_high)
            cam_right_wrist_enc, len_right = images_encoding(cam_right_wrist)
            cam_left_wrist_enc, len_left = images_encoding(cam_left_wrist)
            image.create_dataset('cam_high', data=cam_high_enc, dtype=f'S{len_high}')
            image.create_dataset('cam_right_wrist', data=cam_right_wrist_enc, dtype=f'S{len_right}')
            image.create_dataset('cam_left_wrist', data=cam_left_wrist_enc, dtype=f'S{len_left}')
        begin += 1
        print(f"proccess {i} success!")
    return begin

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='Process some episodes.')
    parser.add_argument('task_name', type=str, default='block_hammer_beat',
                        help='The name of the task (e.g., block_hammer_beat)')
    parser.add_argument('head_camera_type', type=str, default='D435',
                        help='camera type')
    parser.add_argument('expert_data_num', type=int, default=50,
                        help='Number of episodes to process (e.g., 50)')
    args = parser.parse_args()
    
    task_name = args.task_name
    head_camera_type = args.head_camera_type
    num = args.expert_data_num
    
    data_path_name = task_name+"_"+head_camera_type+"_pkl"
    begin = 0
    print(f'read data from path:{os.path.join("data/", data_path_name)}')
    begin = data_transform(os.path.join("data/",data_path_name), num, f"./policy/RDT/processed_data/{task_name}_{head_camera_type}_{num}")
    encode_lang(task_name, f"policy/RDT/processed_data/{task_name}_{head_camera_type}_{num}/",0)